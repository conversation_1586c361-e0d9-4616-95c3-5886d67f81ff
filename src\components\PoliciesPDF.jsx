import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';

// Create styles for the PDF
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 30,
    fontFamily: 'Helvetica',
  },
  header: {
    marginBottom: 20,
    borderBottom: 2,
    borderBottomColor: '#7c977c',
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2d241b',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 18,
    color: '#7c977c',
    marginBottom: 5,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#7c977c',
    marginTop: 20,
    marginBottom: 10,
    borderBottom: 1,
    borderBottomColor: '#a5ab95',
    paddingBottom: 5,
  },
  paragraph: {
    fontSize: 11,
    color: '#2d241b',
    lineHeight: 1.4,
    marginBottom: 8,
  },
  bulletPoint: {
    fontSize: 10,
    color: '#565c4a',
    lineHeight: 1.4,
    marginBottom: 4,
    marginLeft: 15,
  },
  highlightBox: {
    backgroundColor: '#f8faf8',
    padding: 8,
    borderLeft: 3,
    borderLeftColor: '#7c977c',
    marginBottom: 10,
  },
  highlightText: {
    fontSize: 11,
    color: '#2d241b',
    fontWeight: 'bold',
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    borderTop: 1,
    borderTopColor: '#e1e9e1',
    paddingTop: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  footerText: {
    fontSize: 9,
    color: '#565c4a',
  },
});

const PoliciesPDF = () => (
  <Document>
    <Page size="A4" style={styles.page}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Riverdale School of Music</Text>
        <Text style={styles.subtitle}>Studio Policies</Text>
      </View>

      {/* Payment Methods */}
      <Text style={styles.sectionTitle}>Payment Methods</Text>
      <Text style={styles.paragraph}>
        A one-time non-refundable $75 registration fee per family applies upon enrollment. This fee will cover administrative costs, scheduling, materials and event set ups.
      </Text>
      <Text style={styles.paragraph}>
        To secure your preferred lesson day and time during Pre-Registration, a non-refundable tuition deposit will be charged and applied toward your overall tuition.
      </Text>
      <Text style={styles.paragraph}>
        After your deposit is processed to hold your spot, the remaining tuition balance will be divided into equal monthly payments. These payments will be charged on the 1st of each month for the duration of your selected session. The amounts will be prorated until the end of the year for students who sign up mid-year.
      </Text>
      <Text style={styles.paragraph}>
        There will be no registration fee for summer lessons. Students who finish lessons in June will not be charged a re-registration fee, since summer lessons are optional.
      </Text>
      <View style={styles.highlightBox}>
        <Text style={styles.highlightText}>Late Fee:</Text>
        <Text style={styles.paragraph}>
          If a monthly payment cannot be processed for any reason, you will receive an email. Payment must be made in full within 10 days of the failed transaction. If payment is not received within this timeframe, a $30 late fee will be applied and the student will not be allowed to attend private lessons or group classes until the balance is paid.
        </Text>
      </View>

      {/* Lesson Format */}
      <Text style={styles.sectionTitle}>Lesson Format</Text>
      <Text style={styles.paragraph}>
        In Person Only: This option means your lessons will take place at the school only. If we are ever required to move all lessons online (e.g., due to a public health order), your lessons and billing will be paused automatically.
      </Text>
      <Text style={styles.paragraph}>
        Hybrid (Default for Private Lessons): Lessons are primarily in person, but if you ever need to switch to online for a day, just email your teacher before 9 am on the day of your lesson.
      </Text>
      <Text style={styles.paragraph}>
        Online Only: All lessons are held online. If you later choose to return to in-person learning, a change in teacher or schedule may be necessary, depending on availability.
      </Text>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Riverdale School of Music - 300 Danforth Ave, Toronto, ON
        </Text>
        <Text style={styles.footerText} render={({ pageNumber, totalPages }) => 
          `Page ${pageNumber} of ${totalPages}`
        } fixed />
      </View>
    </Page>

    {/* Page 2 */}
    <Page size="A4" style={styles.page}>
      {/* Cancellation Policy */}
      <Text style={styles.sectionTitle}>Cancellation Policy</Text>
      <Text style={styles.paragraph}>
        We aim to balance flexibility for families while respecting our teachers' time. Makeup lessons are only available when we receive at least 24 hours' notice of a cancellation.
      </Text>
      <View style={styles.highlightBox}>
        <Text style={styles.highlightText}>Number of Make-ups Allowed:</Text>
        <Text style={styles.bulletPoint}>• School Year (September 2025 - June 2026): Up to 3 makeup lessons during the 10-month session.</Text>
        <Text style={styles.bulletPoint}>• Winter - Spring 2026 (January - June): Up to 2 makeup lessons during the 6-month session.</Text>
        <Text style={styles.bulletPoint}>• Spring 2026 (March - June): 1 makeup lesson during the 3-month term.</Text>
      </View>
      <Text style={styles.paragraph}>
        Any lessons canceled by the student beyond the number of allowed make-up lessons per registration period will not be rescheduled. This includes absences due to vacations, holidays, or personal scheduling conflicts.
      </Text>

      {/* Lateness */}
      <Text style={styles.sectionTitle}>Lateness</Text>
      <Text style={styles.paragraph}>
        If a student is 15 minutes late with no notice, the teacher is no longer required to wait and may leave the space. The lesson will then be forfeited.
      </Text>

      {/* Snow Days */}
      <Text style={styles.sectionTitle}>Snow Days</Text>
      <Text style={styles.paragraph}>
        In rare cases when it is deemed unsafe to hold lessons (as determined by RSOM), students will be notified by email, and all missed lessons will be rescheduled.
      </Text>

      {/* Holidays */}
      <Text style={styles.sectionTitle}>Holidays</Text>
      <Text style={styles.paragraph}>Dates we are closed:</Text>
      <Text style={styles.bulletPoint}>• December 21– January 3 2026 Inclusive. No private/group lessons.</Text>
      <Text style={styles.bulletPoint}>• March 15 – 21: March Break. No private lessons/group lessons.</Text>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Riverdale School of Music - 300 Danforth Ave, Toronto, ON
        </Text>
        <Text style={styles.footerText} render={({ pageNumber, totalPages }) => 
          `Page ${pageNumber} of ${totalPages}`
        } fixed />
      </View>
    </Page>

    {/* Page 3 */}
    <Page size="A4" style={styles.page}>
      {/* Additional Policies */}
      <Text style={styles.sectionTitle}>Substitute Teachers</Text>
      <Text style={styles.paragraph}>
        RSOM reserves the right to provide a substitute teacher when a regular teacher is ill or unavailable. All substitutes are approved by the director to ensure quality instruction.
      </Text>

      <Text style={styles.sectionTitle}>Supervision Policy</Text>
      <Text style={styles.paragraph}>
        RSOM does not assume responsibility for the care or supervision of students while on school premises. All students under 18 must be accompanied by a parent or caregiver OR have written consent from the RSOM administrator for transfer of care.
      </Text>

      <Text style={styles.sectionTitle}>Photo/Video Consent</Text>
      <Text style={styles.paragraph}>
        By enrolling with RSOM, you consent to photos and videos of your child being taken and used for display at the school, on our website, and social media. To opt out, please email <NAME_EMAIL>
      </Text>

      <Text style={styles.sectionTitle}>Personal Property Policy</Text>
      <Text style={styles.bulletPoint}>• Students and families are responsible for their own belongings, including instruments, books, electronics, and clothing.</Text>
      <Text style={styles.bulletPoint}>• RSOM is not liable for lost, stolen, or damaged property on the premises.</Text>
      <Text style={styles.bulletPoint}>• Label all items with your name. Lost & Found items are kept for 30 days before donation or disposal.</Text>

      <Text style={styles.sectionTitle}>Discontinuing Lessons</Text>
      <Text style={styles.paragraph}>
        If you need to discontinue lessons, email us at least 4 weeks in advance. You'll be billed for the four weeks following your notice. Any unused lessons must be taken within this period or will be forfeited.
      </Text>

      <Text style={styles.sectionTitle}>Code of Conduct</Text>
      <Text style={styles.paragraph}>
        The RSOM Code of Conduct outlines the standards of behaviour expected from all members of our community, including students and staff. We expect students to act with respect for themselves, their peers, their schools, their families, and the wider community.
      </Text>

      <Text style={styles.sectionTitle}>Abiding by Policies</Text>
      <Text style={styles.paragraph}>
        By registering at RSOM, you acknowledge that you have read and agree to comply with all policies outlined above. These policies are subject to change, and any updates will be communicated to you via email.
      </Text>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Riverdale School of Music - 300 Danforth Ave, Toronto, ON
        </Text>
        <Text style={styles.footerText} render={({ pageNumber, totalPages }) => 
          `Page ${pageNumber} of ${totalPages}`
        } fixed />
      </View>
    </Page>
  </Document>
);

export default PoliciesPDF;
