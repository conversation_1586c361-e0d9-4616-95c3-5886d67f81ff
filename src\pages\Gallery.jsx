import { useState } from 'react';

const Gallery = () => {
  const [selectedImage, setSelectedImage] = useState(null);

  const openModal = (image) => {
    setSelectedImage(image);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  return (
    <div className="min-h-screen bg-white dark:bg-primary-900 transition-colors duration-200">
      {/* Hero Section */}
      <section className="relative py-16 bg-white dark:bg-primary-900">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-800/50 dark:to-secondary-800/50"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="font-display text-5xl font-bold mb-6 bg-gradient-to-r from-primary-600 to-secondary-600 dark:from-primary-200 dark:to-secondary-300 bg-clip-text text-transparent">
              Gallery
            </h1>
            <p className="text-xl text-primary-900 dark:text-white">
              Moments and memories from our musical community
            </p>
          </div>
        </div>
      </section>

      {/* Gallery Grid */}
      <section className="py-16 bg-white dark:bg-primary-900 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Gallery items */}
            {images.map((image, index) => (
              <div key={index} className="relative overflow-hidden rounded-lg shadow-lg group cursor-pointer" onClick={() => image.url ? openModal(image) : null}>
                {image.url ? (
                  <img
                    src={image.url}
                    alt={image.alt}
                    className="w-full h-64 object-cover transform group-hover:scale-110 transition-transform duration-200"
                  />
                ) : (
                  <div className="w-full h-64 bg-gray-200 dark:bg-primary-700 flex items-center justify-center">
                    <div className="text-center">
                      <svg className="w-12 h-12 mx-auto mb-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <p className="text-gray-500 dark:text-gray-400 text-sm">Coming Soon</p>
                    </div>
                  </div>
                )}
                <div className="absolute inset-0 bg-gradient-to-t from-primary-900/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <div className="absolute bottom-0 left-0 right-0 p-4">
                    <h3 className="text-white text-lg font-semibold">{image.title}</h3>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Lightbox Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4" onClick={closeModal}>
          <div className="relative max-w-4xl h-full" onClick={(e) => e.stopPropagation()}>
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 text-white hover:text-gray-300 z-10 bg-black bg-opacity-50 rounded-full p-2 transition-colors duration-200"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            <img
              src={selectedImage.url}
              alt={selectedImage.alt}
              className="max-w-full max-h-full object-contain rounded-lg"
            />
            <div className="absolute bottom-4 left-4 right-4 text-center">
              <h3 className="text-white text-xl font-semibold bg-black bg-opacity-50 rounded-lg px-4 py-2">
                {selectedImage.title}
              </h3>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

const images = [
  {
    url: "/gallery-1.jpg",
    alt: "Recital Performance #1!",
    title: "Recital Performance #1!"
  },
  {
    url: "/gallery-2.jpg",
    alt: "Recital Performance #2!",
    title: "Recital Performance #2!"
  },
  {
    url: "/gallery-3.jpeg",
    alt: "Recital Performance #3!",
    title: "Recital Performance #3!"
  },
  {
    url: "/gallery-4.jpeg",
    alt: "Group Class",
    title: "Group Class"
  },
  {
    url: "/gallery-5.jpeg",
    alt: "Music Theory",
    title: "Music Theory"
  },
  {
    url: "/gallery-6.jpeg",
    alt: "Performance",
    title: "Performance"
  },
  {
    url: "/gallery-7.jpeg",
    alt: "Student Collaboration",
    title: "Student Collaboration"
  },
  {
    url: "/gallery-8.jpeg",
    alt: "Ensemble Practice",
    title: "Ensemble Practice"
  }
];

export default Gallery;


