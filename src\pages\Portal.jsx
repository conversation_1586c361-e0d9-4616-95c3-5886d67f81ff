const Portal = () => {
  return (
    <div className="min-h-screen bg-white dark:bg-primary-900 transition-colors duration-200">
      {/* Compact Header Section */}
      <section className="py-12 bg-gray-50 dark:bg-primary-800/30 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-display font-bold mb-4 text-neutral-850 dark:text-white">
              Student & Parent Portal
            </h1>
            <p className="text-lg text-neutral-600 dark:text-gray-300">
              Access your My Music Staff account to manage lessons, view invoices, and more.
            </p>
          </div>
        </div>
      </section>

      {/* Portal Section */}
      <section className="py-6">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            {/* Portal Container */}
            <div className="bg-white dark:bg-primary-800 rounded-xl shadow-lg border border-gray-200 dark:border-primary-700 overflow-hidden">
              <div className="px-6 py-4">
                <h2 className="text-xl font-display font-semibold text-neutral-850 dark:text-white mb-2">
                  My Music Staff Portal
                </h2>
                <p className="text-neutral-600 dark:text-gray-300 mb-4">
                  Log in to manage your account, view invoices, and more.
                </p>

                {/* Iframe Container */}
                <div className="relative">
                  <iframe
                    id="sb_login_if"
                    src="https://app.mymusicstaff.com/Widget/v2/Login.aspx?Sandboxed=false"
                    allowTransparency="true"
                    frameBorder="0"
                    style={{
                      width: '100%',
                      height: '400px',
                      display: 'block',
                      border: '1px solid #ddd',
                      borderRadius: '8px',
                      overflow: 'hidden'
                    }}
                    className="dark:border-primary-600"
                    height="400"
                    scrolling="no"
                    title="My Music Staff Login Portal"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Portal;
