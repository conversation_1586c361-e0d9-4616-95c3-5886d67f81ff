const Portal = () => {
  return (
    <div className="min-h-screen bg-white dark:bg-primary-900 transition-colors duration-200">
      {/* Compact Header Section */}
      <section className="py-12 bg-gray-50 dark:bg-primary-800/30 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="font-display text-2xl md:text-3xl font-bold mb-3 text-neutral-850 dark:text-white">
              Student & Parent Portal
            </h1>
            <p className="text-neutral-600 dark:text-gray-300 leading-relaxed">
              Access your My Music Staff account to manage lessons, view invoices, and more.
            </p>
          </div>
        </div>
      </section>

      {/* Portal Section */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Portal Container */}
            <div className="bg-white dark:bg-primary-800 rounded-xl shadow-lg border border-gray-200 dark:border-primary-700 overflow-hidden">
              <div className="p-4 md:p-6">
                {/* Iframe Container */}
                <div className="relative">
                  <iframe
                    id="sb_login_if"
                    src="https://app.mymusicstaff.com/Widget/v2/Login.aspx?Sandboxed=false"
                    allowTransparency="true"
                    frameBorder="0"
                    className="w-full h-[600px] border border-gray-200 dark:border-primary-600 rounded-lg"
                    height="600"
                    scrolling="auto"
                    title="My Music Staff Login Portal"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Portal;
