import { Link } from 'react-router-dom';
import { useState } from 'react';

const Services = () => {
  const [showFeeInfo, setShowFeeInfo] = useState(false);
  const [showTuitionInfo, setShowTuitionInfo] = useState(false);
  const [showPianoInfo, setShowPianoInfo] = useState(false);
  const [showSingingInfo, setShowSingingInfo] = useState(false);

  return (
    <div className="min-h-screen bg-white dark:bg-primary-900 transition-colors duration-200">
      {/* Hero Section */}
      <section className="relative py-16 bg-white dark:bg-primary-900">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-800/50 dark:to-secondary-800/50"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="font-display text-5xl font-bold mb-6 bg-gradient-to-r from-primary-600 to-secondary-600 dark:from-primary-200 dark:to-secondary-300 bg-clip-text text-transparent">
              Our Lessons
            </h1>
            <p className="text-xl text-primary-900 dark:text-white mb-6">
              Piano and Voice lessons for all skill levels
            </p>
            <button
              onClick={() => setShowFeeInfo(!showFeeInfo)}
              className="inline-flex items-center px-6 py-3 bg-primary-100 dark:bg-primary-700 text-primary-700 dark:text-primary-200 rounded-lg hover:bg-primary-200 dark:hover:bg-primary-600 transition-colors duration-200 mb-4"
            >
              {showFeeInfo ? 'Hide Fee Information' : 'Show Fee Information'}
              <svg className={`w-4 h-4 ml-2 transition-transform ${showFeeInfo ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            {showFeeInfo && (
              <div className="bg-primary-50 dark:bg-primary-800/50 rounded-xl p-8 text-center mb-4">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-lg mb-2 text-neutral-850 dark:text-white">Registration Fee</h4>
                    <p className="text-neutral-600 dark:text-gray-300">
                      <span className="font-bold text-xl text-primary-600 dark:text-primary-400">$50 per family</span><br />
                      <span className="text-sm">(non-refundable, covers admin costs)</span>
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-lg mb-2 text-neutral-850 dark:text-white">Deposit</h4>
                    <p className="text-neutral-600 dark:text-gray-300">
                      <span className="font-bold text-xl text-primary-600 dark:text-primary-400">$100</span><br />
                      <span className="text-sm">(applied toward tuition, secures your spot)</span>
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Piano Lessons */}
      <section className="py-16 bg-white dark:bg-primary-900 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="bg-gray-50 dark:bg-primary-800 rounded-2xl p-8 mb-8">
              <div className="text-center mb-6">
                <div className="w-20 h-20 bg-primary-100 dark:bg-primary-700 rounded-full mx-auto mb-6 flex items-center justify-center">
                  <span className="text-3xl">🎹</span>
                </div>
                <h2 className="text-3xl font-display font-bold mb-4 text-neutral-850 dark:text-white">
                  Piano Lessons
                </h2>
                <p className="text-lg text-neutral-600 dark:text-gray-300 mb-6">
                  Learn piano with our experienced instructors. All skill levels welcome.
                </p>
                <div className="text-center mb-6">
                  <span className="text-2xl font-bold text-primary-600 dark:text-primary-400">$43.50</span>
                  <span className="text-neutral-600 dark:text-gray-300 ml-2">per 30-minute lesson</span>
                </div>
                <button
                  onClick={() => setShowPianoInfo(!showPianoInfo)}
                  className="inline-flex items-center px-6 py-3 bg-primary-100 dark:bg-primary-700 text-primary-700 dark:text-primary-200 rounded-lg hover:bg-primary-200 dark:hover:bg-primary-600 transition-colors duration-200"
                >
                  {showPianoInfo ? 'Hide More Info' : 'More Info'}
                  <svg className={`w-4 h-4 ml-2 transition-transform ${showPianoInfo ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                {showPianoInfo && (
                  <div className="mt-4 text-neutral-700 dark:text-gray-200">
                    <p>
                      Our piano lessons are designed to inspire creativity, build strong foundational technique, and develop confident, well-rounded musicians. Whether you're a beginner or advancing your skills, our experienced teachers tailor each lesson to suit the student's goals, age, and learning style—making the journey both engaging and rewarding.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Singing Lessons */}
      <section className="py-16 bg-gray-50 dark:bg-primary-800/30 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white dark:bg-primary-800 rounded-2xl p-8 mb-8">
              <div className="text-center mb-6">
                <div className="w-20 h-20 bg-primary-100 dark:bg-primary-700 rounded-full mx-auto mb-6 flex items-center justify-center">
                  <span className="text-3xl">🎤</span>
                </div>
                <h2 className="text-3xl font-display font-bold mb-4 text-neutral-850 dark:text-white">
                  Voice Lessons
                </h2>
                <p className="text-lg text-neutral-600 dark:text-gray-300 mb-6">
                  Coming Soon! Develop your voice with professional vocal instruction.
                </p>
                <div className="text-center mb-6">
                  <span className="text-2xl font-bold text-primary-600 dark:text-primary-400">$43.50</span>
                  <span className="text-neutral-600 dark:text-gray-300 ml-2">per 30-minute lesson</span>
                </div>
                <button
                  onClick={() => setShowSingingInfo(!showSingingInfo)}
                  className="inline-flex items-center px-6 py-3 bg-primary-100 dark:bg-primary-700 text-primary-700 dark:text-primary-200 rounded-lg hover:bg-primary-200 dark:hover:bg-primary-600 transition-colors duration-200"
                >
                  {showSingingInfo ? 'Hide More Info' : 'More Info'}
                  <svg className={`w-4 h-4 ml-2 transition-transform ${showSingingInfo ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                {showSingingInfo && (
                  <div className="mt-4 text-neutral-700 dark:text-gray-200">
                    <p>
                      Voice lessons at our school are all about discovering and developing your unique voice. Through fun, focused sessions, students learn how to sing with confidence, control, and emotion. From warm-ups to performance pieces, our instructors guide singers of all levels through techniques that build vocal strength, musicality, and personal expression—whether you're preparing for the stage or simply singing for joy.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Tuition Information */}
      <section className="py-16 bg-white dark:bg-primary-900 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h3 className="text-2xl font-semibold mb-2 text-neutral-850 dark:text-white">
              Tuition Rates (Piano & Voice)
            </h3>
            <p className="text-neutral-600 dark:text-gray-300 mb-6">
              <span className="font-semibold">Based on 38 lessons, September 2025 - June 2026</span>
            </p>
            <div className="overflow-x-auto relative">
              <div className="block md:hidden text-xs text-primary-700 dark:text-primary-200 mb-2 text-right pr-4">
                <span>Swipe to see more &rarr;</span>
              </div>
              <table className="min-w-full border-separate border-spacing-y-2">
                <thead>
                  <tr className="bg-primary-100 dark:bg-primary-800/50">
                    <th className="py-3 px-4 rounded-l-lg text-left text-sm font-semibold text-primary-900 dark:text-white">Lesson Length</th>
                    <th className="py-3 px-4 text-center text-sm font-semibold text-primary-900 dark:text-white">Cost per Lesson</th>
                    <th className="py-3 px-4 text-center text-sm font-semibold text-primary-900 dark:text-white">Total Tuition</th>
                    <th className="py-3 px-4 text-center text-sm font-semibold text-primary-900 dark:text-white">Balance After Deposit</th>
                    <th className="py-3 px-4 rounded-r-lg text-center text-sm font-semibold text-primary-900 dark:text-white">10 Monthly Payments</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="bg-gray-50 dark:bg-primary-800">
                    <td className="py-3 px-4 rounded-l-lg font-medium text-left">30 Minutes</td>
                    <td className="py-3 px-4 text-center">$43.50</td>
                    <td className="py-3 px-4 text-center">$1,653.00</td>
                    <td className="py-3 px-4 text-center">$1,553.00</td>
                    <td className="py-3 px-4 rounded-r-lg text-center">$155.30</td>
                  </tr>
                  <tr className="bg-gray-50 dark:bg-primary-800">
                    <td className="py-3 px-4 rounded-l-lg font-medium text-left">45 Minutes</td>
                    <td className="py-3 px-4 text-center">$63.50</td>
                    <td className="py-3 px-4 text-center">$2,451.00</td>
                    <td className="py-3 px-4 text-center">$2,351.00</td>
                    <td className="py-3 px-4 rounded-r-lg text-center">$235.10</td>
                  </tr>
                  <tr className="bg-gray-50 dark:bg-primary-800">
                    <td className="py-3 px-4 rounded-l-lg font-medium text-left">60 Minutes</td>
                    <td className="py-3 px-4 text-center">$87.00</td>
                    <td className="py-3 px-4 text-center">$3,306.00</td>
                    <td className="py-3 px-4 text-center">$3,206.00</td>
                    <td className="py-3 px-4 rounded-r-lg text-center">$320.60</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div className="mt-4 text-sm text-neutral-600 dark:text-gray-300">
              <div>
                <span className="font-semibold">Deposit:</span> $100 (applied toward tuition, secures your spot)
              </div>
              <div>
                <span className="font-semibold">Registration Fee:</span> $50 per family (non-refundable, covers admin costs)
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Get Started Section */}
      <section className="py-12 bg-gray-50 dark:bg-primary-800/30 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <Link to="/contact">
              <button className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg font-medium transition-colors duration-200 text-lg">
                Get Started Today
              </button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Services;