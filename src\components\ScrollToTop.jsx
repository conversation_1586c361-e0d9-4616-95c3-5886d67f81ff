import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const ScrollToTop = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    // Disable scroll restoration first
    if ('scrollRestoration' in history) {
      history.scrollRestoration = 'manual';
    }

    // Multiple approaches to ensure mobile browsers scroll to top

    // Method 1: Immediate scroll to top
    window.scrollTo(0, 0);

    // Method 2: Force scroll on document elements (for mobile)
    document.documentElement.scrollTop = 0;
    document.body.scrollTop = 0;

    // Method 3: Use requestAnimationFrame for better mobile compatibility
    requestAnimationFrame(() => {
      window.scrollTo(0, 0);
      document.documentElement.scrollTop = 0;
      document.body.scrollTop = 0;
    });

    // Method 4: Delayed scroll for stubborn mobile browsers
    const timeoutId = setTimeout(() => {
      window.scrollTo(0, 0);
      document.documentElement.scrollTop = 0;
      document.body.scrollTop = 0;
    }, 50);

    // Method 5: Additional delay for iOS Safari
    const timeoutId2 = setTimeout(() => {
      window.scrollTo(0, 0);
    }, 150);

    return () => {
      clearTimeout(timeoutId);
      clearTimeout(timeoutId2);
    };
  }, [pathname]);

  return null;
};

export default ScrollToTop;
