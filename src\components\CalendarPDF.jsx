import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';

// Create styles for the PDF
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 30,
    fontFamily: 'Helvetica',
  },
  header: {
    marginBottom: 20,
    borderBottom: 2,
    borderBottomColor: '#7c977c',
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2d241b',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 18,
    color: '#7c977c',
    marginBottom: 5,
  },
  description: {
    fontSize: 12,
    color: '#565c4a',
  },
  yearSection: {
    marginTop: 20,
    marginBottom: 15,
  },
  yearTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#7c977c',
    marginBottom: 15,
    borderBottom: 1,
    borderBottomColor: '#a5ab95',
    paddingBottom: 5,
  },
  eventContainer: {
    marginBottom: 12,
    paddingLeft: 10,
  },
  eventDate: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#2d241b',
    marginBottom: 3,
  },
  eventDescription: {
    fontSize: 11,
    color: '#565c4a',
    lineHeight: 1.4,
  },
  recitalEvent: {
    backgroundColor: '#f8faf8',
    padding: 8,
    borderLeft: 3,
    borderLeftColor: '#7c977c',
    marginBottom: 12,
  },
  recitalDate: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#7c977c',
    marginBottom: 3,
  },
  recitalDescription: {
    fontSize: 11,
    color: '#2d241b',
    lineHeight: 1.4,
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    borderTop: 1,
    borderTopColor: '#e1e9e1',
    paddingTop: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  footerText: {
    fontSize: 9,
    color: '#565c4a',
  },
});

const CalendarPDF = () => (
  <Document>
    <Page size="A4" style={styles.page}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Riverdale School of Music</Text>
        <Text style={styles.subtitle}>Studio Calendar 2025-2026</Text>
        <Text style={styles.description}>School Year Important Dates</Text>
      </View>

      {/* 2025 Events */}
      <View style={styles.yearSection}>
        <Text style={styles.yearTitle}>2025</Text>
        
        <View style={styles.eventContainer}>
          <Text style={styles.eventDate}>September 2</Text>
          <Text style={styles.eventDescription}>
            Private lessons begin (10 months / 39 lessons)
          </Text>
        </View>

        <View style={styles.eventContainer}>
          <Text style={styles.eventDate}>October 13</Text>
          <Text style={styles.eventDescription}>
            Thanksgiving Monday - Private lessons take place as usual
          </Text>
        </View>

        <View style={styles.eventContainer}>
          <Text style={styles.eventDate}>December 22 - January 4, 2026</Text>
          <Text style={styles.eventDescription}>
            No private lessons (school is closed)
          </Text>
        </View>
      </View>

      {/* 2026 Events */}
      <View style={styles.yearSection}>
        <Text style={styles.yearTitle}>2026</Text>
        
        <View style={styles.eventContainer}>
          <Text style={styles.eventDate}>January 5</Text>
          <Text style={styles.eventDescription}>
            Private lessons recommence
          </Text>
        </View>

        {/* Winter Recital - Special Styling */}
        <View style={styles.recitalEvent}>
          <Text style={styles.recitalDate}>February 7-8</Text>
          <Text style={styles.recitalDescription}>
            Winter Recital
          </Text>
        </View>

        <View style={styles.eventContainer}>
          <Text style={styles.eventDate}>February 16</Text>
          <Text style={styles.eventDescription}>
            Family Day – Private lessons take place as usual
          </Text>
        </View>

        <View style={styles.eventContainer}>
          <Text style={styles.eventDate}>March 15-21</Text>
          <Text style={styles.eventDescription}>
            March Break – School will be closed. No Private lessons
          </Text>
        </View>

        <View style={styles.eventContainer}>
          <Text style={styles.eventDate}>March 22</Text>
          <Text style={styles.eventDescription}>
            Private lessons recommence
          </Text>
        </View>

        <View style={styles.eventContainer}>
          <Text style={styles.eventDate}>April 3-6</Text>
          <Text style={styles.eventDescription}>
            Easter Weekend – Private lessons take place as usual
          </Text>
        </View>

        {/* Spring Recital - Special Styling */}
        <View style={styles.recitalEvent}>
          <Text style={styles.recitalDate}>June 6</Text>
          <Text style={styles.recitalDescription}>
            Spring Recital
          </Text>
        </View>

        <View style={styles.eventContainer}>
          <Text style={styles.eventDate}>June 22</Text>
          <Text style={styles.eventDescription}>
            Last day of Private lessons
          </Text>
        </View>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Riverdale School of Music - 300 Danforth Ave, Toronto, ON
        </Text>
        <Text style={styles.footerText} render={({ pageNumber, totalPages }) => 
          `Page ${pageNumber} of ${totalPages}`
        } fixed />
      </View>
    </Page>
  </Document>
);

export default CalendarPDF;
