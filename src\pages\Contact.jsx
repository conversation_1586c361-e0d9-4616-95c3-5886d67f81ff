import { useRef, useState } from 'react';
import emailjs from 'emailjs-com';

const Contact = () => {
  const form = useRef();
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    
    emailjs.sendForm(
      import.meta.env.VITE_EMAILJS_SERVICE_ID,
      import.meta.env.VITE_EMAILJS_TEMPLATE_ID,
      form.current,
      import.meta.env.VITE_EMAILJS_PUBLIC_KEY
    )
      .then((result) => {
        console.log('Email sent successfully');
        setIsSubmitted(true);
      })
      .catch((error) => {
        console.error('Error sending email:', error);
      });
  };

  const SuccessMessage = () => (
    <div className="text-center py-16 space-y-6">
      <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full mx-auto flex items-center justify-center">
        <svg className="w-10 h-10 text-green-500 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
        </svg>
      </div>
      <h3 className="text-2xl font-display font-bold text-neutral-850 dark:text-white">
        Message Sent Successfully!
      </h3>
      <p className="text-neutral-600 dark:text-gray-300">
        Thank you for reaching out. We'll get back to you soon!
      </p>
      <button
        onClick={() => {
          setIsSubmitted(false);
          form.current.reset();
        }}
        className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium"
      >
        Send another message
      </button>
    </div>
  );

  return (
    <div className="min-h-screen bg-white dark:bg-neutral-850 transition-colors duration-200">
      {/* Hero Section */}
      <section className="relative py-20 bg-white dark:bg-neutral-850">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-gray-800 dark:to-gray-900 transition-colors duration-200"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="font-display text-5xl font-bold mb-6 bg-gradient-to-r from-primary-600 to-secondary-600 dark:from-primary-400 dark:to-secondary-400 bg-clip-text text-transparent">
              Contact Us
            </h1>
            <p className="text-xl text-neutral-850 dark:text-white">
              We'd love to hear from you
            </p>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-white dark:bg-neutral-850 transition-colors duration-200">
        <div className="container mx-auto px-4 max-w-6xl">
          <div className="bg-white dark:bg-neutral-800 rounded-2xl shadow-xl overflow-hidden">
            <div className="grid md:grid-cols-5">
              {/* Contact Information */}
              <div className="md:col-span-2 bg-gradient-to-br from-primary-500 to-primary-600 dark:from-gray-800 dark:to-gray-900 p-8 text-white">
                <h2 className="text-2xl font-display font-bold mb-8">
                  Get in Touch
                </h2>
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <svg className="w-6 h-6 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <div>
                      <h3 className="font-semibold mb-1">Location</h3>
                      <p className="text-white/80">300 Danforth Ave</p>
                      <p className="text-white/80">Toronto, ON M4K 1N6</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4">
                    <svg className="w-6 h-6 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <div>
                      <h3 className="font-semibold mb-1">Email</h3>
                      <p className="text-white/80"><EMAIL></p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4">
                    <svg className="w-6 h-6 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                      <h3 className="font-semibold mb-1">Hours</h3>
                      <p className="text-white/80">Monday - Friday: 3:00 PM - 8:00 PM</p>
                      <p className="text-white/80">Saturday: 9:00 AM - 5:00 PM</p>
                      <p className="text-white/80">Sunday: 9:00 AM - 5:00 PM</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Contact Form */}
              <div className="md:col-span-3 p-8 bg-white dark:bg-neutral-800">
                <h2 className="text-2xl font-display font-bold mb-8 text-neutral-850 dark:text-white">
                  Send Us a Message
                </h2>
                
                {isSubmitted ? (
                  <SuccessMessage />
                ) : (
                  <form ref={form} onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-neutral-850 dark:text-white mb-2">
                          Student Name *
                        </label>
                        <input
                          type="text"
                          name="student_name"
                          className="w-full px-4 py-3 rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-700 text-neutral-850 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-neutral-850 dark:text-white mb-2">
                          Student Age
                        </label>
                        <input
                          type="number"
                          name="student_age"
                          min="5"
                          max="100"
                          className="w-full px-4 py-3 rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-700 text-neutral-850 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                        />
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-neutral-850 dark:text-white mb-2">
                          Parent/Guardian Name *
                        </label>
                        <input
                          type="text"
                          name="parent_name"
                          className="w-full px-4 py-3 rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-700 text-neutral-850 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-neutral-850 dark:text-white mb-2">
                          Email Address *
                        </label>
                        <input
                          type="email"
                          name="email"
                          className="w-full px-4 py-3 rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-700 text-neutral-850 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-neutral-850 dark:text-white mb-2">
                          Phone Number
                        </label>
                        <input
                          type="tel"
                          name="phone"
                          className="w-full px-4 py-3 rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-700 text-neutral-850 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-neutral-850 dark:text-white mb-2">
                          Lesson of Interest *
                        </label>
                        <select
                          name="instrument"
                          className="w-full px-4 py-3 rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-700 text-neutral-850 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                          required
                        >
                          <option value="">Select an instrument</option>
                          <option value="piano">Piano</option>
                          <option value="singing">Singing</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-neutral-850 dark:text-white mb-2">
                        Previous Musical Experience
                      </label>
                      <select
                        name="experience"
                        className="w-full px-4 py-3 rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-700 text-neutral-850 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                      >
                        <option value="">Select experience level</option>
                        <option value="beginner">Complete Beginner</option>
                        <option value="some">Some Experience</option>
                        <option value="intermediate">Intermediate</option>
                        <option value="advanced">Advanced</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-neutral-850 dark:text-white mb-2">
                        Preferred Lesson Length
                      </label>
                      <div className="grid grid-cols-3 gap-4">
                        <label className="flex items-center">
                          <input type="radio" name="lesson_length" value="30" className="mr-2" />
                          <span className="text-neutral-700 dark:text-gray-300">30 minutes</span>
                        </label>
                        <label className="flex items-center">
                          <input type="radio" name="lesson_length" value="45" className="mr-2" />
                          <span className="text-neutral-700 dark:text-gray-300">45 minutes</span>
                        </label>
                        <label className="flex items-center">
                          <input type="radio" name="lesson_length" value="60" className="mr-2" />
                          <span className="text-neutral-700 dark:text-gray-300">60 minutes</span>
                        </label>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-neutral-850 dark:text-white mb-2">
                        Additional Comments or Questions
                      </label>
                      <textarea
                        name="message"
                        rows="4"
                        className="w-full px-4 py-3 rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-700 text-neutral-850 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Tell us about your musical goals, preferred styles, or any questions you have..."
                      ></textarea>
                    </div>

                    <button
                      type="submit"
                      className="w-full bg-primary-600 hover:bg-primary-700 text-white py-4 rounded-lg font-medium transition-colors duration-200"
                    >
                      Send Message
                    </button>

                    <p className="text-sm text-neutral-500 dark:text-gray-400 text-center">
                      * Required fields.
                    </p>
                  </form>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="py-16 bg-gray-50 dark:bg-primary-800/30 transition-colors duration-200">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-display font-bold text-center mb-8 text-neutral-850 dark:text-white">
              Find Us
            </h2>
            <div className="bg-white dark:bg-neutral-800 rounded-2xl shadow-lg overflow-hidden">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2884.4567890123456!2d-79.3496!3d43.6777!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89d4cb90d7c63ba5%3A0x323555fdd78a9c0e!2s300%20Danforth%20Ave%2C%20Toronto%2C%20ON%20M4K%201N6%2C%20Canada!5e0!3m2!1sen!2sus!4v1234567890123!5m2!1sen!2sus"
                width="100%"
                height="400"
                style={{ border: 0 }}
                allowFullScreen=""
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                className="w-full"
                title="Riverdale School of Music Location"
              ></iframe>
            </div>
            <div className="text-center mt-6">
              <p className="text-neutral-600 dark:text-gray-300 mb-4">
                Located in the heart of Riverdale, easily accessible by TTC
              </p>
              <a
                href="https://maps.google.com/?q=300+Danforth+Ave,+Toronto,+ON+Canada"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors duration-200"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Open in Google Maps
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;






